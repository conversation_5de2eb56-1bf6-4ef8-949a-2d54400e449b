import pandas as pd

def analyze_ontology_data(filename):
    """分析本体数据，统计本体数量和详细信息"""
    try:
        df = pd.read_excel(filename)
        print(f"文件读取成功！")
        print(f"数据总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        print("=" * 80)
        
        # 获取A列（本体列）的所有非空值
        ontology_column = df.iloc[:, 0]  # A列是第0列
        
        # 找出所有非空的本体名称
        unique_ontologies = []
        current_ontology = None
        
        for index, value in ontology_column.items():
            if pd.notna(value) and str(value).strip() != '':
                current_ontology = str(value).strip()
                if current_ontology not in unique_ontologies:
                    unique_ontologies.append(current_ontology)
                    print(f"本体 {len(unique_ontologies)}: {current_ontology}")
        
        print("=" * 80)
        print(f"总共发现 {len(unique_ontologies)} 个不同的本体")
        print("=" * 80)
        
        # 详细分析每个本体的属性数量
        print("各本体的数据属性统计:")
        print("-" * 80)
        
        ontology_stats = {}
        current_ontology = None
        attribute_count = 0
        
        for index, row in df.iterrows():
            ontology_name = row.iloc[0]  # A列
            
            if pd.notna(ontology_name) and str(ontology_name).strip() != '':
                # 如果前一个本体存在，记录其属性数量
                if current_ontology is not None:
                    ontology_stats[current_ontology] = attribute_count
                
                # 开始新的本体
                current_ontology = str(ontology_name).strip()
                attribute_count = 1  # 当前行也算一个属性
            else:
                # 继续当前本体的属性计数
                if current_ontology is not None:
                    attribute_count += 1
        
        # 记录最后一个本体的属性数量
        if current_ontology is not None:
            ontology_stats[current_ontology] = attribute_count
        
        # 输出统计结果
        total_attributes = 0
        for i, (ontology, count) in enumerate(ontology_stats.items(), 1):
            print(f"{i:2d}. {ontology:<20} - {count:3d} 个数据属性")
            total_attributes += count
        
        print("-" * 80)
        print(f"总计: {len(ontology_stats)} 个本体, {total_attributes} 个数据属性")
        
        return unique_ontologies, ontology_stats
        
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")
        return None, None

if __name__ == "__main__":
    ontologies, stats = analyze_ontology_data('zk本体.xlsx')
