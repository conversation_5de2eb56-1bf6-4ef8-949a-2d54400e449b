#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys

def read_excel_file(filename):
    """读取Excel文件并显示内容"""
    try:
        # 读取Excel文件
        df = pd.read_excel(filename)
        
        print(f"文件读取成功！")
        print(f"数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        print("\n" + "="*80)
        print("Excel文件内容:")
        print("="*80)
        
        # 显示所有数据
        for index, row in df.iterrows():
            print(f"行 {index + 1}:")
            for col_idx, col_name in enumerate(df.columns):
                value = row[col_name] if pd.notna(row[col_name]) else ""
                print(f"  {chr(65 + col_idx)}列 ({col_name}): {value}")
            print("-" * 40)
            
        return df
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

if __name__ == "__main__":
    filename = "zk本体.xlsx"
    df = read_excel_file(filename)
